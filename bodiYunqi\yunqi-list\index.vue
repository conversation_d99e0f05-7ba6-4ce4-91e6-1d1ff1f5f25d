<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input
					class="search-input"
					type="text"
					placeholder="搜索线索名称或手机号"
					v-model="searchKeyword"
					@input="handleSearch"
					@confirm="handleSearch"
				/>
				<view class="search-clear" v-if="searchKeyword" @tap="clearSearch">
					<text class="clear-icon">✕</text>
				</view>
			</view>
		</view>

		<!-- Tab 切换 -->
		<tn-sticky>
			<view class="tab-header">
				<tn-tabs :list="tabList" :current="currentTab" :isScroll="false" name="name" @change="tabChange"
					activeColor="#01BEFF" inactiveColor="#666666" :bold="true" backgroundColor="#ffffff"
					:bar-width="100" :fontSize="32"></tn-tabs>
			</view>
		</tn-sticky>

		<!-- 列表内容 -->
		<view class="list-content">
			<!-- 我的代办 -->
			<view v-if="currentTab === 0" class="tab-content">
				<view class="list-header">
					<text class="list-title">我的代办</text>
					<text class="list-count">共 {{myTodoList.length}} 条</text>
				</view>
				<scroll-view class="scroll-wrapper" scroll-y @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="pagination.myTodo.loading && pagination.myTodo.pageNo === 1">
					<view class="list-wrapper">
						<view v-for="(item, index) in myTodoList" :key="index" class="list-item-container">
						<view class="item-content" @tap="handleItemClick(item, 'myTodo')">
							<view class="item-header">
								<view class="item-title">{{item.clue_name}}</view>
								<view class="item-status" :class="'status-' + getStatusText(item.state)">
									{{item.state }}
								</view>
							</view>
							<view class="item-details">
								<!-- <view class="detail-row">
									<text class="detail-label">销售：</text>
									<text class="detail-value">{{item.real_name}}</text>
								</view> -->
								<view class="detail-row" v-if="item.customerPhone">
									<text class="detail-label">客户电话：</text>
									<text class="detail-value">{{item.phone}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">当前节点：</text>
									<text class="detail-value">{{item.work_name}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">负责人：</text>
									<text class="detail-value">{{item.duty_user_name}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">开启时间：</text>
									<text class="detail-value">{{item.start_time || '暂无设置'}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">结束时间：</text>
									<text class="detail-value">{{item.end_time || '暂无设置'}}</text>
								</view>
								<view class="detail-row" v-if="item.alarm" style="color: red;">
									<text class="detail-label">报警信息：</text>
									<text class="detail-value alert-text">{{item.alarm}}</text>
								</view>
                                <view class="detail-row" v-if="item.remark" style="color: cornflowerblue;">
									<text class="detail-label">提示信息：</text>
									<text class="detail-value alert-text">{{item.remark}}</text>
								</view>
							</view>
						</view>
						<view class="item-actions">
                            <template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleAction(index, cItem, type)">
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
							</template>
						</view>
					</view>
					<view v-if="myTodoList.length === 0 && !pagination.myTodo.loading" class="empty-state">
						<text class="empty-text">暂无代办事项</text>
					</view>

					<!-- 加载更多提示 -->
					<view v-if="myTodoList.length > 0" class="load-more">
						<view v-if="pagination.myTodo.loading" class="loading">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="!pagination.myTodo.hasMore" class="no-more">
							<text class="no-more-text">已加载全部数据</text>
						</view>
						<view v-else class="pull-more">
							<text class="pull-more-text">上拉加载更多</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

			<!-- 员工代办 -->
			<view v-if="currentTab === 1" class="tab-content">
				<view class="list-header">
					<text class="list-title">员工代办</text>
					<text class="list-count">共 {{employeeTodoList.length}} 条</text>
				</view>
				<scroll-view
					class="scroll-wrapper"
					scroll-y
					@scrolltolower="loadMore"
					refresher-enabled
					@refresherrefresh="onRefresh"
					:refresher-triggered="pagination.employeeTodo.loading && pagination.employeeTodo.pageNo === 1">
					<view class="list-wrapper">
						<view v-for="(item, index) in employeeTodoList" :key="index" class="list-item-container">
						<view class="item-content" @tap="handleItemClick(item, 'employeeTodo')">
							<view class="item-header">
								<view class="item-title">{{item.clue_name}}</view>
								<view class="item-status" :class="'status-' + item.state">
									{{getStatusText(item.state)}}
								</view>
							</view>
							<view class="item-details">
								<view class="detail-row">
									<text class="detail-label">销售人：</text>
									<text class="detail-value">{{item.salesperson}}</text>
								</view>
								<view class="detail-row" v-if="item.customerPhone">
									<text class="detail-label">客户电话：</text>
									<text class="detail-value">{{item.customerPhone}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">当前节点：</text>
									<text class="detail-value">{{item.currentNode}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">负责人：</text>
									<text class="detail-value">{{item.nodeOwner}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">开启时间：</text>
									<text class="detail-value">{{item.startTime}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">截止时间：</text>
									<text class="detail-value">{{item.deadline}}</text>
								</view>
								<view class="detail-row" v-if="item.alertInfo">
									<text class="detail-label">报警信息：</text>
									<text class="detail-value alert-text">{{item.alertInfo}}</text>
								</view>
							</view>
						</view>
						<view class="item-actions">
							<template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleAction(index, cItem, type)">
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
							</template>
						</view>
					</view>
					<view v-if="employeeTodoList.length === 0 && !pagination.employeeTodo.loading" class="empty-state">
						<text class="empty-text">暂无员工代办事项</text>
					</view>

					<!-- 加载更多提示 -->
					<view v-if="employeeTodoList.length > 0" class="load-more">
						<view v-if="pagination.employeeTodo.loading" class="loading">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="!pagination.employeeTodo.hasMore" class="no-more">
							<text class="no-more-text">已加载全部数据</text>
						</view>
						<view v-else class="pull-more">
							<text class="pull-more-text">上拉加载更多</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

			<!-- 我的客户 -->
			<view v-if="currentTab === 2" class="tab-content">
				<view class="list-header">
					<text class="list-title">我的客户</text>
					<text class="list-count">共 {{myCustomerList.length}} 条</text>
				</view>
				<scroll-view
					class="scroll-wrapper"
					scroll-y
					@scrolltolower="loadMore"
					refresher-enabled
					@refresherrefresh="onRefresh"
					:refresher-triggered="pagination.myCustomer.loading && pagination.myCustomer.pageNo === 1">
					<view class="list-wrapper">
						<view v-for="(item, index) in myCustomerList" :key="index" class="list-item-container">
						<view class="item-content" @tap="handleItemClick(item, 'myCustomer')">
							<view class="item-header">
								<view class="item-title">{{item.leadName}}</view>
								<view class="item-status" :class="'status-' + item.status">
									{{getCustomerStatusText(item.status)}}
								</view>
							</view>
							<view class="item-details">
								<view class="detail-row">
									<text class="detail-label">销售人：</text>
									<text class="detail-value">{{item.salesperson}}</text>
								</view>
								<view class="detail-row" v-if="item.customerPhone">
									<text class="detail-label">客户电话：</text>
									<text class="detail-value">{{item.customerPhone}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">当前节点：</text>
									<text class="detail-value">{{item.currentNode}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">负责人：</text>
									<text class="detail-value">{{item.nodeOwner}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">开启时间：</text>
									<text class="detail-value">{{item.startTime}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">截止时间：</text>
									<text class="detail-value">{{item.deadline}}</text>
								</view>
								<view class="detail-row" v-if="item.alertInfo">
									<text class="detail-label">报警信息：</text>
									<text class="detail-value alert-text">{{item.alertInfo}}</text>
								</view>
							</view>
						</view>
						<view class="item-actions">
							<template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleAction(index, cItem, cItem.type)">
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
							</template>
						</view>
					</view>
					<view v-if="myCustomerList.length === 0 && !pagination.myCustomer.loading" class="empty-state">
						<text class="empty-text">暂无客户信息</text>
					</view>

					<!-- 加载更多提示 -->
					<view v-if="myCustomerList.length > 0" class="load-more">
						<view v-if="pagination.myCustomer.loading" class="loading">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="!pagination.myCustomer.hasMore" class="no-more">
							<text class="no-more-text">已加载全部数据</text>
						</view>
						<view v-else class="pull-more">
							<text class="pull-more-text">上拉加载更多</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

			<!-- 员工客户 -->
			<view v-if="currentTab === 3" class="tab-content">
				<view class="list-header">
					<text class="list-title">员工客户</text>
					<text class="list-count">共 {{employeeCustomerList.length}} 条</text>
				</view>
				<scroll-view
					class="scroll-wrapper"
					scroll-y
					@scrolltolower="loadMore"
					refresher-enabled
					@refresherrefresh="onRefresh"
					:refresher-triggered="pagination.employeeCustomer.loading && pagination.employeeCustomer.pageNo === 1">
					<view class="list-wrapper">
						<view v-for="(item, index) in employeeCustomerList" :key="index" class="list-item-container">
						<view class="item-content" @tap="handleItemClick(item, 'employeeCustomer')">
							<view class="item-header">
								<view class="item-title">{{item.leadName}}</view>
								<view class="item-status" :class="'status-' + item.status">
									{{getCustomerStatusText(item.status)}}
								</view>
							</view>
							<view class="item-details">
								<view class="detail-row">
									<text class="detail-label">销售人：</text>
									<text class="detail-value">{{item.salesperson}}</text>
								</view>
								<view class="detail-row" v-if="item.customerPhone">
									<text class="detail-label">客户电话：</text>
									<text class="detail-value">{{item.customerPhone}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">当前节点：</text>
									<text class="detail-value">{{item.currentNode}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">负责人：</text>
									<text class="detail-value">{{item.nodeOwner}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">开启时间：</text>
									<text class="detail-value">{{item.startTime}}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">截止时间：</text>
									<text class="detail-value">{{item.deadline}}</text>
								</view>
								<view class="detail-row" v-if="item.alertInfo">
									<text class="detail-label">报警信息：</text>
									<text class="detail-value alert-text">{{item.alertInfo}}</text>
								</view>
							</view>
						</view>
						<view class="item-actions">
							<template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleAction(index, cItem, type)">
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
							</template>
						</view>
					</view>
					<view v-if="employeeCustomerList.length === 0 && !pagination.employeeCustomer.loading" class="empty-state">
						<text class="empty-text">暂无员工客户信息</text>
					</view>

					<!-- 加载更多提示 -->
					<view v-if="employeeCustomerList.length > 0" class="load-more">
						<view v-if="pagination.employeeCustomer.loading" class="loading">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="!pagination.employeeCustomer.hasMore" class="no-more">
							<text class="no-more-text">已加载全部数据</text>
						</view>
						<view v-else class="pull-more">
							<text class="pull-more-text">上拉加载更多</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</view>
</template>

<script>
	export default {
		name: 'ListIndex',
		data() {
			return {
				// 当前选中的tab索引
				currentTab: 0,
				// 搜索关键词
				searchKeyword: '',
				// 搜索防抖定时器
				searchTimer: null,
				// tab列表配置
				tabList: [{
						name: '我的代办'
					},
					{
						name: '员工代办'
					},
					{
						name: '我的客户'
					},
					{
						name: '员工客户'
					}
				],
				// 我的代办列表
				myTodoList: [],
				// 员工代办列表
				employeeTodoList: [],
				// 我的客户列表
				myCustomerList: [],
				// 员工客户列表
				employeeCustomerList: [],
				// 分页参数
				pagination: {
					myTodo: { pageNo: 1, pageSize: 10, hasMore: true, loading: false },
					employeeTodo: { pageNo: 1, pageSize: 10, hasMore: true, loading: false },
					myCustomer: { pageNo: 1, pageSize: 10, hasMore: true, loading: false },
					employeeCustomer: { pageNo: 1, pageSize: 10, hasMore: true, loading: false }
				},
				// API接口映射
				apiMap: {
					0: 'EstateWork/my_wait',
					1: 'EstateWork/staff_wait',
					2: 'EstateWork/my_custom',
					3: 'EstateWork/staff_custom'
				}
			}
		},
		onLoad() {
			this.loadData()
		},

		onReachBottom() {
			this.loadMore()
		},

		beforeDestroy() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
		},
		methods: {
			// tab切换事件
			tabChange(index) {
				this.currentTab = index
				this.resetPagination()
				this.loadData()
			},

			// 搜索处理
			handleSearch() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer)
				}
				// 设置防抖，500ms后执行搜索
				this.searchTimer = setTimeout(() => {
					this.resetPagination()
					this.loadData()
				}, 500)
			},

			// 清除搜索
			clearSearch() {
				this.searchKeyword = ''
				this.resetPagination()
				this.loadData()
			},

			// 重置分页参数
			resetPagination() {
				const currentType = this.getCurrentType()
				this.pagination[currentType].pageNo = 1
				this.pagination[currentType].hasMore = true
				this.pagination[currentType].loading = false

				// 清空对应的列表
				switch (this.currentTab) {
					case 0:
						this.myTodoList = []
						break
					case 1:
						this.employeeTodoList = []
						break
					case 2:
						this.myCustomerList = []
						break
					case 3:
						this.employeeCustomerList = []
						break
				}
			},

			// 获取当前类型
			getCurrentType() {
				const typeMap = {
					0: 'myTodo',
					1: 'employeeTodo',
					2: 'myCustomer',
					3: 'employeeCustomer'
				}
				return typeMap[this.currentTab]
			},

			// 加载数据
			loadData(isLoadMore = false) {
				const currentType = this.getCurrentType()
				const pagination = this.pagination[currentType]

				// 如果正在加载或没有更多数据，则不执行
				if (pagination.loading || (!isLoadMore && !pagination.hasMore && pagination.pageNo > 1)) {
					return
				}

				pagination.loading = true

				// 构建请求参数
				const params = {
					pageNo: pagination.pageNo,
					pageSize: pagination.pageSize,
					search: this.searchKeyword.trim()
				}

				// 调用对应的API
				const apiUrl = this.apiMap[this.currentTab]
				this.$api.request(apiUrl, params, (res) => {
					pagination.loading = false

					if (res.status === 'ok') {
						const newData = res.list || []

						// 根据当前tab更新对应的列表
						switch (this.currentTab) {
							case 0:
								if (isLoadMore) {
									this.myTodoList = [...this.myTodoList, ...newData]
								} else {
									this.myTodoList = newData
								}
								break
							case 1:
								if (isLoadMore) {
									this.employeeTodoList = [...this.employeeTodoList, ...newData]
								} else {
									this.employeeTodoList = newData
								}
								break
							case 2:
								if (isLoadMore) {
									this.myCustomerList = [...this.myCustomerList, ...newData]
								} else {
									this.myCustomerList = newData
								}
								break
							case 3:
								if (isLoadMore) {
									this.employeeCustomerList = [...this.employeeCustomerList, ...newData]
								} else {
									this.employeeCustomerList = newData
								}
								break
						}

						// 更新分页状态
						if (newData.length < pagination.pageSize) {
							pagination.hasMore = false
						} else {
							pagination.pageNo++
						}
					} else {
						this.$api.toast(res.info || '加载失败')
					}
				})
			},

			// 加载更多数据
			loadMore() {
				this.loadData(true)
			},

			// 下拉刷新
			onRefresh() {
				this.resetPagination()
				this.loadData()
			},



			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'待提交': 'pending',
					'处理中': 'processing',
					'已完成': 'completed',
					'已取消': 'cancelled'
				}
                console.log(statusMap[status], '12333333333')
				return statusMap[status] || 'info'
			},

			// 获取客户状态文本
			getCustomerStatusText(status) {
				const statusMap = {
					'active': '活跃客户',
					'potential': '潜在客户',
					'inactive': '非活跃',
					'lost': '流失客户'
				}
				return statusMap[status] || '未知'
			},

			// 列表项点击事件
			handleItemClick(item, type) {
				console.log('点击了项目:', item, '类型:', type)
				// 跳转到详情页面
				uni.navigateTo({
					url: `/bodiYunqi/yunqi-detail/index?id=${item.id}&type=${type}`
				})
			},

			// 按钮操作事件
			handleAction(item, action, type) {
				console.log('操作:', action, '项目:', item, '类型:', type)
                switch(type) {
					case 'tel':
						// 拨打电话
						uni.makePhoneCall({
							phoneNumber: action.param.tel
						})
						break
					default:
						uni.showToast({
							title: `${action}功能开发中`,
							icon: 'none'
					})
				}
				// switch(action) {
				//	case 'edit':
				//	case 'modify':
				//		this.handleEdit(item, type)
				//		break
				//	case 'view':
				//		this.handleView(item, type)
				//		break
				//	case 'process':
				//		this.handleProcess(item, type)
				//		break
				//	case 'assign':
				//		this.handleAssign(item, type)
				//		break
				//	case 'contract':
				//		this.handleContract(item, type)
				//		break
				//	default:
				//		uni.showToast({
				//			title: `${action}功能开发中`,
				//			icon: 'none'
				//	})
				//}
			},

			// 编辑/修改
			handleEdit(item, type) {
				uni.showToast({
					title: '跳转到编辑页面',
					icon: 'none'
				})
				// 实际应该跳转到编辑页面
				// uni.navigateTo({
				//   url: `/pages/edit/index?id=${item.id}&type=${type}`
				// })
			},

			// 查看详情
			handleView(item, type) {
				uni.navigateTo({
					url: `/bodiYunqi/yunqi-detail/index?id=${item.id}&type=${type}`
				})
			},

			// 处理
			handleProcess(item, type) {
				uni.showToast({
					title: '跳转到处理页面',
					icon: 'none'
				})
				// 实际应该跳转到处理页面
				// uni.navigateTo({
				//   url: `/pages/process/index?id=${item.id}&type=${type}`
				// })
			},

			// 分配
			handleAssign(item, type) {
				uni.showToast({
					title: '跳转到分配页面',
					icon: 'none'
				})
				// 实际应该跳转到分配页面
				// uni.navigateTo({
				//   url: `/pages/assign/index?id=${item.id}&type=${type}`
				// })
			},

			// 签合同
			handleContract(item, type) {
				uni.showToast({
					title: '跳转到合同页面',
					icon: 'none'
				})
				// 实际应该跳转到合同页面
				// uni.navigateTo({
				//   url: `/pages/contract/index?id=${item.id}&type=${type}`
				// })
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	

	.search-box {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		padding: 20rpx 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.search-icon {
		font-size: 32rpx;
		color: #999999;
		margin-right: 20rpx;
	}

	.search-input {
		flex: 1;
		font-size: 28rpx;
		color: #333333;

		&::placeholder {
			color: #999999;
		}
	}

	.search-clear {
		padding: 10rpx;
		margin-left: 20rpx;

		&:active {
			opacity: 0.6;
		}
	}

	.clear-icon {
		font-size: 28rpx;
		color: #999999;
	}

	.tab-header {
		background-color: #ffffff;
		border-bottom: 1rpx solid #e5e5e5;
	}

	.list-content {
		padding: 20rpx;
	}

	.tab-content {
		background-color: transparent;
	}

	.list-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		margin-bottom: 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.list-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.list-count {
		font-size: 28rpx;
		color: #666666;
	}

	.list-wrapper {
		padding: 0;
	}

	.list-item-container {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

		&:last-child {
			margin-bottom: 0;
		}
	}

	.item-content {
		padding: 30rpx;

		&:active {
			background-color: #f8f8f8;
		}
	}

	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 20rpx;
	}

	.item-details {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.detail-row {
		display: flex;
		align-items: center;
	}

	.detail-label {
		font-size: 24rpx;
		color: #666666;
		min-width: 140rpx;
		flex-shrink: 0;
	}

	.detail-value {
		font-size: 24rpx;
		color: #333333;
		flex: 1;
	}

	.alert-text {
		color: #ff3b30;
		font-weight: 500;
	}

	.item-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		flex: 1;
		margin-right: 20rpx;
		line-height: 1.4;
	}

	.item-desc {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 15rpx;
	}

	.item-info {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.item-time {
		font-size: 24rpx;
		color: #999999;
	}

	.item-employee {
		font-size: 24rpx;
		color: #666666;
	}

	.item-status {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		color: #ffffff;

		&.status-pending {
			background-color: #ff9500;
		}

		&.status-processing {
			background-color: #007aff;
		}

		&.status-completed {
			background-color: #34c759;
		}

		&.status-cancelled {
			background-color: #ff3b30;
		}

		&.status-active {
			background-color: #34c759;
		}

		&.status-potential {
			background-color: #007aff;
		}

		&.status-inactive {
			background-color: #8e8e93;
		}

		&.status-lost {
			background-color: #ff3b30;
		}
	}

	.item-actions {
		display: flex;
		flex-direction: row-reverse;
		gap: 12rpx;
		padding: 0 30rpx 30rpx 30rpx;
		background-color: #ffffff;
		justify-content: flex-start;
	}

	.action-btn {
		padding: 10rpx 16rpx;
		border-radius: 6rpx;
		text-align: center;
		font-size: 22rpx;
		border: 1rpx solid transparent;
		min-width: 80rpx;

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}
	}

	.btn-text {
		color: #ffffff;
		font-size: 24rpx;
	}

	.edit-btn {
		background-color: #007aff;

		.btn-text {
			color: #ffffff;
		}
	}

	.view-btn {
		background-color: #34c759;

		.btn-text {
			color: #ffffff;
		}
	}

	.process-btn {
		background-color: #ff9500;

		.btn-text {
			color: #ffffff;
		}
	}

	.assign-btn {
		background-color: #5856d6;

		.btn-text {
			color: #ffffff;
		}
	}

	.contract-btn {
		background-color: #ff3b30;

		.btn-text {
			color: #ffffff;
		}
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 100rpx 30rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}

	.scroll-wrapper {
		height: calc(100vh - 300rpx);
	}

	.load-more {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 30rpx;
		background-color: #f5f5f5;
	}

	.loading, .no-more, .pull-more {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.loading-text, .no-more-text, .pull-more-text {
		font-size: 24rpx;
		color: #999999;
	}

	.loading-text {
		color: #007aff;
	}
</style>