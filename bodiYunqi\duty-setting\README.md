# 职能设置页面

## 页面功能
- 显示职能列表
- 支持设置、清空、换人操作
- 根据负责人状态显示不同的操作按钮

## 接口调用
- **接口地址**: `EstateWork/dutylisting`
- **请求方式**: GET
- **返回数据格式**:
```javascript
{
  count: 2,
  info: "职能列表成功",
  status: "ok",
  list: [
    {
      id: 3,
      name: "销售",
      dept_name: "销售", 
      real_name: "王岩",
      avatar: null,
      state: "正常",
      state_value: 2
    },
    {
      id: 4,
      name: "工程",
      dept_name: "销售",
      real_name: null,
      avatar: null,
      state: "未设置", 
      state_value: 1
    }
  ]
}
```

## 页面布局
- **卡片列表**: 每个职能一张卡片
- **左侧**: 头像（如果有avatar则显示图片，否则显示姓名首字母）
- **中间**: 职能名称、部门名称、负责人信息、状态
- **右侧**: 操作按钮

## 按钮逻辑
- **未设置负责人** (`state_value: 1`): 只显示"设置"按钮
- **已设置负责人** (`state_value: 2`): 显示"清空"和"换人"按钮

## 操作功能
1. **设置**: 跳转到人员选择页面（待实现）
2. **清空**: 弹出确认框，确认后调用清空API（待实现）
3. **换人**: 跳转到人员选择页面（待实现）

## 样式特点
- 使用卡片布局，简洁美观
- 头像支持图片和文字占位符
- 状态标签有不同颜色区分
- 按钮使用渐变色彩

## 待完善功能
1. 人员选择页面的实现
2. 清空负责人的API调用
3. 设置/更换负责人的API调用
4. 操作成功后的列表刷新

## 使用方式
将页面放在 `bodiYunqi/duty-setting/index.vue`，然后在需要的地方跳转：

```javascript
uni.navigateTo({
  url: '/bodiYunqi/duty-setting/index'
})
```
