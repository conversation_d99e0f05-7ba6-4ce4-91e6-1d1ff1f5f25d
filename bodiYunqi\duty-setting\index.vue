<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">职能设置</text>
		</view>

		<!-- 职能列表 -->
		<view class="duty-list">
			<view 
				v-for="(item, index) in dutyList" 
				:key="item.id" 
				class="duty-card"
			>
				<!-- 左侧头像 -->
				<view class="avatar-section">
					<image 
						v-if="item.avatar" 
						:src="item.avatar" 
						class="avatar"
						mode="aspectFill"
					/>
					<view v-else class="avatar-placeholder">
						<text class="avatar-text">{{ item.real_name ? item.real_name.charAt(0) : item.name.charAt(0) }}</text>
					</view>
				</view>

				<!-- 右侧信息 -->
				<view class="info-section">
					<view class="duty-info">
						<view class="duty-name">{{ item.name }}</view>
						<view class="dept-name">{{ item.dept_name }}</view>
					</view>
					
					<view class="person-info">
						<view v-if="item.real_name" class="person-name">
							负责人：{{ item.real_name }}
						</view>
						<view v-else class="no-person">
							暂未设置负责人
						</view>
						<view class="status" :class="'status-' + item.state_value">
							{{ item.state }}
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-section">
					<!-- 未设置负责人时只显示设置按钮 -->
					<view v-if="item.state_value === 1" class="btn-group">
						<tn-button 
							backgroundColor="tn-main-gradient-blue" 
							fontColor="#fff" 
							size="sm"
							@click="handleSetPerson(item)"
						>
							设置
						</tn-button>
					</view>
					
					<!-- 已设置负责人时显示清空和换人按钮 -->
					<view v-else class="btn-group">
						<tn-button 
							backgroundColor="tn-main-gradient-orangered" 
							fontColor="#fff" 
							size="sm"
							@click="handleClearPerson(item)"
						>
							清空
						</tn-button>
						<tn-button 
							backgroundColor="tn-main-gradient-green" 
							fontColor="#fff" 
							size="sm"
							@click="handleChangePerson(item)"
						>
							换人
						</tn-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="dutyList.length === 0 && !loading" class="empty-state">
			<text class="empty-text">暂无职能数据</text>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-state">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'DutySetting',
		data() {
			return {
				dutyList: [],
				loading: false,
				flowCustomId: 2 // 默认值为2，可通过页面参数传递
			}
		},
		onLoad(options) {
			// 如果页面跳转时传递了flow_custom_id参数，则使用传递的值
			if (options.flow_custom_id) {
				this.flowCustomId = options.flow_custom_id
			}
			this.loadDutyList()
		},
		methods: {
			// 加载职能列表
			loadDutyList() {
				this.loading = true

				const params = {
					flow_custom_id: this.flowCustomId
				}

				this.$api.request('EstateWork/dutylisting', params, (res) => {
					this.loading = false
					if (res.status === 'ok') {
						this.dutyList = res.list || []
						console.log('职能列表:', this.dutyList)
					} else {
						this.$api.toast(res.info || '加载失败')
					}
				})
			},

			// 设置负责人
			handleSetPerson(item) {
				console.log('设置负责人:', item)
				uni.showToast({
					title: '跳转到人员选择页面',
					icon: 'none'
				})
				// TODO: 跳转到人员选择页面
				// uni.navigateTo({
				//   url: `/pages/person-select/index?dutyId=${item.id}&dutyName=${item.name}`
				// })
			},

			// 清空负责人
			handleClearPerson(item) {
				uni.showModal({
					title: '确认清空',
					content: `确定要清空"${item.name}"的负责人吗？`,
					success: (res) => {
						if (res.confirm) {
							this.clearPersonApi(item)
						}
					}
				})
			},

			// 换人
			handleChangePerson(item) {
				console.log('换人:', item)
				uni.showToast({
					title: '跳转到人员选择页面',
					icon: 'none'
				})
				// TODO: 跳转到人员选择页面
				// uni.navigateTo({
				//   url: `/pages/person-select/index?dutyId=${item.id}&dutyName=${item.name}&currentPerson=${item.real_name}`
				// })
			},

			// 清空负责人API调用
			clearPersonApi(item) {
				// TODO: 调用清空负责人的API
				console.log('调用清空API:', item.id)
				uni.showToast({
					title: '清空成功',
					icon: 'success'
				})
				// 刷新列表
				this.loadDutyList()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.page-header {
		padding: 20rpx 0;
		text-align: center;
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
	}

	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	.duty-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.duty-card {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 24rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.avatar-section {
		margin-right: 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
	}

	.avatar-placeholder {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #01BEFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.avatar-text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: bold;
	}

	.info-section {
		flex: 1;
		margin-right: 20rpx;
	}

	.duty-info {
		margin-bottom: 12rpx;
	}

	.duty-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 4rpx;
	}

	.dept-name {
		font-size: 24rpx;
		color: #666666;
	}

	.person-info {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.person-name {
		font-size: 28rpx;
		color: #333333;
	}

	.no-person {
		font-size: 28rpx;
		color: #999999;
	}

	.status {
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		
		&.status-1 {
			background-color: #fff3cd;
			color: #856404;
		}
		
		&.status-2 {
			background-color: #d4edda;
			color: #155724;
		}
	}

	.action-section {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	.btn-group {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 100rpx 30rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}

	.loading-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 100rpx 30rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #666666;
	}
</style>
